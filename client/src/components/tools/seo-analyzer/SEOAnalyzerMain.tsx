import React, { useState, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { SEOAnalysisForm } from "./components/SEOAnalysisForm";
import { SEOProgressDisplay } from "./components/SEOProgressDisplay";
import { SEOErrorDisplay } from "./components/SEOErrorDisplay";
import { SEOResultsTabs } from "./components/SEOResultsTabs";
import { useSEOAnalysis } from "./hooks/useSEOAnalysis";
import { usePersistentAnalysis } from "./hooks/usePersistentAnalysis";
import { useSEOAnalysisHistory } from "./hooks/useSEOAnalysisHistory";
import { calculateSEOScore, copyToClipboard as copyToClipboardUtil } from "./utils/seo-helpers";
import { SEOAnalysisResult, AnalysisMode } from "./types/seo";
import { CreateSEOAnalysisData } from "@/types/seoAnalysisTypes";
import SEOAgent from "@/components/tools/seo-agent";
import SEOAnalysisDashboard from "@/components/tools/seo-analysis-dashboard";
import { SEOAnalysisCard } from "@/components/tools/SEOAnalysisCard";



export const SEOAnalyzerMain: React.FC = () => {
  const { toast } = useToast();
  const { user, isAuthenticated, isLoading: isAuthLoading } = useAuth();

  // State
  const [url, setUrl] = useState("");
  const [analysisMode, setAnalysisMode] = useState<AnalysisMode>("page");
  const [analysisResult, setAnalysisResult] = useState<SEOAnalysisResult | null>(null);
  const [activeTab, setActiveTab] = useState("analyzer");

  // History and favorites hooks (must be called before using saveAnalysis)
  const {
    recentAnalyses,
    isLoadingRecent,
    recentError,
    refetchRecent,
    favoriteAnalyses,
    isLoadingFavorites,
    favoritesError,
    refetchFavorites,
    saveAnalysis,
    updateAnalysis,
    deleteAnalysis,
    toggleFavorite,
    recordView,
    isSaving,
    isUpdating,
    isDeleting
  } = useSEOAnalysisHistory();

  // Analysis completion handlers
  const handleAnalysisComplete = useCallback(async (result: SEOAnalysisResult) => {
    setAnalysisResult(result);

    // Save analysis to database if user is authenticated
    if (isAuthenticated && user?.id && result.status === 'success') {
      try {
        const analysisData: CreateSEOAnalysisData = {
          user_id: user.id,
          url: result.url,
          analysis_mode: analysisMode,
          tool_type: 'seo_analyzer',
          analysis_version: '1.0',
          overall_score: calculateSEOScore(result),
          basic_info: result.basic_info,
          content_analysis: result.content_analysis,
          seo_checks: result.seo_checks,
          recommendations: result.recommendations,
          achievements: result.achievements || [],
          open_graph: result.open_graph || {},
          twitter_card: result.twitter_card || {},
          preview_data: result.preview_data || {},
          performance_metrics: result.performance_metrics,
          ai_enhanced: result.ai_enhanced || false,
          status: 'completed'
        };

        console.log('💾 Saving SEO analysis to database:', analysisData);
        await saveAnalysis(analysisData);

        toast({
          title: "Análisis completado y guardado",
          description: "El análisis SEO se ha completado y guardado en tu historial",
        });
      } catch (saveError) {
        console.error('❌ Failed to save SEO analysis:', saveError);
        toast({
          title: "Análisis completado",
          description: "El análisis se completó pero no se pudo guardar en el historial",
          variant: "destructive",
        });
      }
    } else {
      toast({
        title: "Análisis completado",
        description: "El análisis SEO se ha completado exitosamente",
      });
    }
  }, [toast, isAuthenticated, user?.id, analysisMode, saveAnalysis]);

  const handleAnalysisError = useCallback((error: string) => {
    toast({
      title: "Error en el análisis",
      description: error,
      variant: "destructive",
    });
  }, [toast]);

  // Hooks
  const {
    data,
    isLoading,
    isError,
    error,
    analyzeUrl,
    showPulsatingEffect,
    setShowPulsatingEffect,
  } = useSEOAnalysis({ url, analysisMode });

  const {
    persistentAnalysisId,
    persistentProgress,
    persistentLoading,
    persistentError,
    progressLoading,
    progressError,
    startPersistentAnalysis,
    cancelPersistentAnalysis,
    clearPersistentAnalysis,
  } = usePersistentAnalysis({
    url,
    analysisMode,
    onAnalysisComplete: handleAnalysisComplete,
    onAnalysisError: handleAnalysisError,
  });



  // Handle analysis based on mode
  const handleAnalyze = useCallback(() => {
    if (analysisMode === "page") {
      analyzeUrl();
    } else {
      startPersistentAnalysis();
    }
  }, [analysisMode, analyzeUrl, startPersistentAnalysis]);

  // Get current data (either from React Query or persistent analysis)
  const currentData = analysisResult || data;

  // Calculate SEO score
  const getSEOScore = useCallback((): number => {
    return calculateSEOScore(currentData);
  }, [currentData]);

  // Copy to clipboard with toast
  const copyToClipboard = useCallback(async (text: string) => {
    const success = await copyToClipboardUtil(text);
    if (success) {
      toast({
        title: "Copiado",
        description: "Contenido copiado al portapapeles",
      });
    } else {
      toast({
        title: "Error",
        description: "No se pudo copiar al portapapeles",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Check if any loading state is active
  const isAnyLoading = isLoading || progressLoading || persistentLoading;
  const hasError = isError || !!progressError || !!persistentError;
  const hasResults = (currentData && currentData.status === "success");

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="analyzer">Analizador</TabsTrigger>
          <TabsTrigger
            value="history"
            disabled={!isAuthenticated && process.env.NODE_ENV === 'production'}
          >
            Historial
          </TabsTrigger>
          <TabsTrigger
            value="favorites"
            disabled={!isAuthenticated && process.env.NODE_ENV === 'production'}
          >
            Favoritos
          </TabsTrigger>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
        </TabsList>

        <TabsContent value="analyzer" className="space-y-6">
          {/* Analysis Form */}
          <SEOAnalysisForm
            url={url}
            setUrl={setUrl}
            analysisMode={analysisMode}
            setAnalysisMode={setAnalysisMode}
            onAnalyze={handleAnalyze}
            isLoading={isLoading}
            progressLoading={progressLoading}
            persistentLoading={persistentLoading}
            showPulsatingEffect={showPulsatingEffect}
          />

          {/* Progress Display */}
          <SEOProgressDisplay
            isLoading={isLoading}
            progressLoading={progressLoading}
            persistentLoading={persistentLoading}
            persistentProgress={persistentProgress}
            onCancel={cancelPersistentAnalysis}
          />

          {/* Error Display */}
          <SEOErrorDisplay
            error={error}
            progressError={progressError}
            persistentError={persistentError}
            isError={isError}
          />

          {/* Results */}
          {hasResults && (
            <>
              <SEOResultsTabs
                data={currentData}
                url={url}
                getSEOScore={getSEOScore}
                copyToClipboard={copyToClipboard}
              />

              {/* Save to Favorites Button */}
              {isAuthenticated && (
                <div className="flex justify-center">
                  <button
                    onClick={async () => {
                      if (!user?.id || !currentData) return;

                      try {
                        const analysisData: CreateSEOAnalysisData = {
                          user_id: user.id,
                          url: currentData.url,
                          analysis_mode: analysisMode,
                          tool_type: 'seo_analyzer',
                          analysis_version: '1.0',
                          overall_score: calculateSEOScore(currentData),
                          basic_info: currentData.basic_info,
                          content_analysis: currentData.content_analysis,
                          seo_checks: currentData.seo_checks,
                          recommendations: currentData.recommendations,
                          achievements: currentData.achievements || [],
                          open_graph: currentData.open_graph || {},
                          twitter_card: currentData.twitter_card || {},
                          preview_data: currentData.preview_data || {},
                          performance_metrics: currentData.performance_metrics,
                          ai_enhanced: currentData.ai_enhanced || false,
                          status: 'completed',
                          is_favorite: true
                        };

                        await saveAnalysis(analysisData);
                        toast({
                          title: "Guardado en favoritos",
                          description: "El análisis se guardó en tus favoritos"
                        });
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "No se pudo guardar en favoritos",
                          variant: "destructive"
                        });
                      }
                    }}
                    disabled={isSaving}
                    className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ? "Guardando..." : "Guardar en Favoritos"}
                  </button>
                </div>
              )}
            </>
          )}

          {/* SEO Agent */}
          {hasResults && (
            <SEOAgent
              analysisComplete={true}
              seoScore={getSEOScore()}
              numIssues={currentData?.recommendations?.length || 0}
            />
          )}

          {!currentData && !isAnyLoading && !hasError && (
            <SEOAgent analysisComplete={false} />
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Historial de Análisis SEO</h2>
              <span className="text-sm text-muted-foreground">
                Últimos 10 análisis
              </span>
            </div>

            {!isAuthenticated && process.env.NODE_ENV === 'production' ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  Inicia sesión para ver tu historial de análisis
                </p>
              </div>
            ) : isLoadingRecent ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Cargando historial...</p>
              </div>
            ) : recentError ? (
              <div className="text-center py-8">
                <p className="text-destructive">Error al cargar el historial</p>
              </div>
            ) : recentAnalyses.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  No hay análisis en el historial. ¡Realiza tu primer análisis SEO!
                </p>
              </div>
            ) : (
              <div className="grid gap-4">
                {recentAnalyses.map((analysis) => (
                  <SEOAnalysisCard
                    key={analysis.id}
                    analysis={analysis}
                    onLoad={(analysis) => {
                      setUrl(analysis.url);
                      setAnalysisMode(analysis.analysis_mode);
                      setAnalysisResult({
                        status: 'success',
                        url: analysis.url,
                        basic_info: analysis.basic_info,
                        content_analysis: analysis.content_analysis,
                        seo_checks: analysis.seo_checks,
                        recommendations: analysis.recommendations,
                        achievements: analysis.achievements || [],
                        open_graph: analysis.open_graph || {},
                        twitter_card: analysis.twitter_card || {},
                        preview_data: analysis.preview_data || {},
                        performance_metrics: analysis.performance_metrics,
                        ai_enhanced: analysis.ai_enhanced || false
                      });
                      setActiveTab('analyzer');
                      recordView(analysis.id);
                    }}
                    onToggleFavorite={async (analysis) => {
                      try {
                        await toggleFavorite(analysis.id);
                        toast({
                          title: analysis.is_favorite ? "Eliminado de favoritos" : "Añadido a favoritos",
                          description: analysis.is_favorite
                            ? "El análisis se eliminó de tus favoritos"
                            : "El análisis se añadió a tus favoritos"
                        });
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "No se pudo actualizar el estado de favorito",
                          variant: "destructive"
                        });
                      }
                    }}
                    onDelete={async (analysis) => {
                      try {
                        await deleteAnalysis(analysis.id);
                        toast({
                          title: "Análisis eliminado",
                          description: "El análisis se eliminó correctamente"
                        });
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "No se pudo eliminar el análisis",
                          variant: "destructive"
                        });
                      }
                    }}
                    toolType="seo_analyzer"
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="favorites" className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Análisis SEO Favoritos</h2>
              <span className="text-sm text-muted-foreground">
                {favoriteAnalyses.length} favorito{favoriteAnalyses.length !== 1 ? 's' : ''}
              </span>
            </div>

            {!isAuthenticated && process.env.NODE_ENV === 'production' ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  Inicia sesión para ver tus análisis favoritos
                </p>
              </div>
            ) : isLoadingFavorites ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Cargando favoritos...</p>
              </div>
            ) : favoritesError ? (
              <div className="text-center py-8">
                <p className="text-destructive">Error al cargar los favoritos</p>
              </div>
            ) : favoriteAnalyses.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  No tienes análisis favoritos. Marca algunos análisis como favoritos para verlos aquí.
                </p>
              </div>
            ) : (
              <div className="grid gap-4">
                {favoriteAnalyses.map((analysis) => (
                  <SEOAnalysisCard
                    key={analysis.id}
                    analysis={analysis}
                    onLoad={(analysis) => {
                      setUrl(analysis.url);
                      setAnalysisMode(analysis.analysis_mode);
                      setAnalysisResult({
                        status: 'success',
                        url: analysis.url,
                        basic_info: analysis.basic_info,
                        content_analysis: analysis.content_analysis,
                        seo_checks: analysis.seo_checks,
                        recommendations: analysis.recommendations,
                        achievements: analysis.achievements || [],
                        open_graph: analysis.open_graph || {},
                        twitter_card: analysis.twitter_card || {},
                        preview_data: analysis.preview_data || {},
                        performance_metrics: analysis.performance_metrics,
                        ai_enhanced: analysis.ai_enhanced || false
                      });
                      setActiveTab('analyzer');
                      recordView(analysis.id);
                    }}
                    onToggleFavorite={async (analysis) => {
                      try {
                        await toggleFavorite(analysis.id);
                        toast({
                          title: "Eliminado de favoritos",
                          description: "El análisis se eliminó de tus favoritos"
                        });
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "No se pudo actualizar el estado de favorito",
                          variant: "destructive"
                        });
                      }
                    }}
                    onDelete={async (analysis) => {
                      try {
                        await deleteAnalysis(analysis.id);
                        toast({
                          title: "Análisis eliminado",
                          description: "El análisis se eliminó correctamente"
                        });
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "No se pudo eliminar el análisis",
                          variant: "destructive"
                        });
                      }
                    }}
                    toolType="seo_analyzer"
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="dashboard" className="space-y-6">
          <SEOAnalysisDashboard />
        </TabsContent>


      </Tabs>
    </div>
  );
};
