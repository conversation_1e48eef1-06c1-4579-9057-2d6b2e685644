/**
 * Hook for managing SEO analysis history and favorites
 * Following the established patterns from headline analyzer
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '@/hooks/use-auth'
import { seoAnalysisService } from '@/services/seoAnalysisService'
import type { SEOAnalysis, CreateSEOAnalysisData, UpdateSEOAnalysisData } from '@/types/seoAnalysisTypes'

interface UseSEOAnalysisHistoryReturn {
  // Recent analyses
  recentAnalyses: SEOAnalysis[]
  isLoadingRecent: boolean
  recentError: Error | null
  refetchRecent: () => void

  // Favorite analyses
  favoriteAnalyses: SEOAnalysis[]
  isLoadingFavorites: boolean
  favoritesError: Error | null
  refetchFavorites: () => void

  // Actions
  saveAnalysis: (data: CreateSEOAnalysisData) => Promise<SEOAnalysis>
  updateAnalysis: (data: UpdateSEOAnalysisData) => Promise<SEOAnalysis>
  deleteAnalysis: (id: string) => Promise<void>
  toggleFavorite: (id: string) => Promise<SEOAnalysis>
  recordView: (id: string) => Promise<void>

  // Loading states
  isSaving: boolean
  isUpdating: boolean
  isDeleting: boolean
}

export const useSEOAnalysisHistory = (): UseSEOAnalysisHistoryReturn => {
  const { user, isAuthenticated } = useAuth()
  const queryClient = useQueryClient()

  // Single query for all analyses (following headline analyzer pattern)
  const {
    data: queryData,
    isLoading: isLoadingAnalyses,
    error: analysesError,
    refetch: refetchAnalyses
  } = useQuery({
    queryKey: ['seo-analyses', user?.id],
    queryFn: async () => {
      console.log('🔍 useSEOAnalysisHistory: Fetching all analyses for user:', user?.id);
      try {
        const result = await seoAnalysisService.getUserAnalyses(user?.id || '', {
          limit: 100,
          orderBy: 'created_at',
          orderDirection: 'desc'
        });
        console.log('✅ useSEOAnalysisHistory: Successfully fetched analyses:', result.length);
        console.log('📊 useSEOAnalysisHistory: Result type:', typeof result, Array.isArray(result) ? 'is array' : 'not array');
        return result;
      } catch (error) {
        console.error('❌ useSEOAnalysisHistory: Error fetching analyses:', error);
        throw error;
      }
    },
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
  })

  // Ensure allAnalyses is always an array with defensive programming
  const allAnalyses = useMemo(() => {
    console.log('🔄 useSEOAnalysisHistory: Processing query data:', typeof queryData, queryData);

    if (!queryData) {
      console.log('📊 useSEOAnalysisHistory: No query data, returning empty array');
      return [];
    }

    if (Array.isArray(queryData)) {
      console.log('✅ useSEOAnalysisHistory: Query data is array with', queryData.length, 'items');
      return queryData;
    }

    // Handle case where data might be wrapped in an object
    if (queryData && typeof queryData === 'object' && Array.isArray(queryData.analyses)) {
      console.log('✅ useSEOAnalysisHistory: Query data has analyses array with', queryData.analyses.length, 'items');
      return queryData.analyses;
    }

    console.warn('⚠️ useSEOAnalysisHistory: Unexpected query data format:', queryData);
    return [];
  }, [queryData])

  // Computed values using useMemo (following headline analyzer pattern)
  const favoriteAnalyses = useMemo(() => {
    // Defensive programming: ensure allAnalyses is an array
    if (!Array.isArray(allAnalyses)) {
      console.warn('⚠️ useSEOAnalysisHistory: allAnalyses is not an array:', typeof allAnalyses, allAnalyses);
      return [];
    }
    return allAnalyses.filter(analysis => analysis.is_favorite);
  }, [allAnalyses]);

  const recentAnalyses = useMemo(() => {
    // Defensive programming: ensure allAnalyses is an array
    if (!Array.isArray(allAnalyses)) {
      console.warn('⚠️ useSEOAnalysisHistory: allAnalyses is not an array:', typeof allAnalyses, allAnalyses);
      return [];
    }
    // Get non-favorite analyses, limited to 10 most recent
    const nonFavorites = allAnalyses.filter(analysis => !analysis.is_favorite);
    return nonFavorites.slice(0, 10);
  }, [allAnalyses]);

  // Use the computed loading and error states
  const isLoadingRecent = isLoadingAnalyses;
  const isLoadingFavorites = isLoadingAnalyses;
  const recentError = analysesError as Error | null;
  const favoritesError = analysesError as Error | null;
  const refetchRecent = refetchAnalyses;
  const refetchFavorites = refetchAnalyses;

  // Save analysis mutation
  const saveAnalysisMutation = useMutation({
    mutationFn: (data: CreateSEOAnalysisData) => seoAnalysisService.saveAnalysis(data),
    onSuccess: (newAnalysis) => {
      console.log('✅ useSEOAnalysisHistory: Analysis saved successfully:', newAnalysis.id);
      // Invalidate and refetch analyses (following headline analyzer pattern)
      queryClient.invalidateQueries({ queryKey: ['seo-analyses', user?.id] });
    },
    onError: (error) => {
      console.error('❌ useSEOAnalysisHistory: Error saving SEO analysis:', error)
    }
  })

  // Update analysis mutation
  const updateAnalysisMutation = useMutation({
    mutationFn: (data: UpdateSEOAnalysisData) => seoAnalysisService.updateAnalysis(data),
    onSuccess: (updatedAnalysis) => {
      console.log('✅ useSEOAnalysisHistory: Analysis updated successfully:', updatedAnalysis.id);
      // Update the analysis in the cache (following headline analyzer pattern)
      queryClient.setQueryData(['seo-analyses', user?.id], (oldData: SEOAnalysis[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(analysis =>
          analysis.id === updatedAnalysis.id ? updatedAnalysis : analysis
        );
      });
    },
    onError: (error) => {
      console.error('❌ useSEOAnalysisHistory: Error updating SEO analysis:', error)
    }
  })

  // Delete analysis mutation
  const deleteAnalysisMutation = useMutation({
    mutationFn: (id: string) => seoAnalysisService.deleteAnalysis(id),
    onSuccess: (_, deletedId) => {
      console.log('✅ useSEOAnalysisHistory: Analysis deleted successfully:', deletedId);
      // Remove the analysis from the cache (following headline analyzer pattern)
      queryClient.setQueryData(['seo-analyses', user?.id], (oldData: SEOAnalysis[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.filter(analysis => analysis.id !== deletedId);
      });
    },
    onError: (error) => {
      console.error('❌ useSEOAnalysisHistory: Error deleting SEO analysis:', error)
    }
  })

  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: (id: string) => seoAnalysisService.toggleFavorite(id),
    onSuccess: (updatedAnalysis) => {
      console.log('✅ useSEOAnalysisHistory: Favorite toggled successfully:', updatedAnalysis.id);
      // Update the analysis in the cache (following headline analyzer pattern)
      queryClient.setQueryData(['seo-analyses', user?.id], (oldData: SEOAnalysis[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(analysis =>
          analysis.id === updatedAnalysis.id ? updatedAnalysis : analysis
        );
      });
    },
    onError: (error) => {
      console.error('❌ useSEOAnalysisHistory: Error toggling favorite:', error)
    }
  })

  // Record view function
  const recordView = useCallback(async (id: string) => {
    try {
      await seoAnalysisService.recordView(id)

      // Update view count in cache (following headline analyzer pattern)
      queryClient.setQueryData(['seo-analyses', user?.id], (oldData: SEOAnalysis[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(analysis =>
          analysis.id === id
            ? { ...analysis, view_count: (analysis.view_count || 0) + 1, last_viewed_at: new Date().toISOString() }
            : analysis
        );
      });
    } catch (error) {
      console.error('❌ Error recording view:', error)
      // Don't throw - view recording shouldn't break the app
    }
  }, [queryClient, user?.id])

  return {
    // Data
    recentAnalyses,
    isLoadingRecent,
    recentError: recentError as Error | null,
    refetchRecent,

    favoriteAnalyses,
    isLoadingFavorites,
    favoritesError: favoritesError as Error | null,
    refetchFavorites,

    // Actions
    saveAnalysis: saveAnalysisMutation.mutateAsync,
    updateAnalysis: updateAnalysisMutation.mutateAsync,
    deleteAnalysis: deleteAnalysisMutation.mutateAsync,
    toggleFavorite: toggleFavoriteMutation.mutateAsync,
    recordView,

    // Loading states
    isSaving: saveAnalysisMutation.isPending,
    isUpdating: updateAnalysisMutation.isPending,
    isDeleting: deleteAnalysisMutation.isPending
  }
}
