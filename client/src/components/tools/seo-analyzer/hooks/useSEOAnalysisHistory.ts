/**
 * Hook for managing SEO analysis history and favorites
 * Following the established patterns from headline analyzer
 */

import { useState, useEffect, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '@/hooks/use-auth'
import { seoAnalysisService } from '@/services/seoAnalysisService'
import type { SEOAnalysis, CreateSEOAnalysisData, UpdateSEOAnalysisData } from '@/types/seoAnalysisTypes'

interface UseSEOAnalysisHistoryReturn {
  // Recent analyses
  recentAnalyses: SEOAnalysis[]
  isLoadingRecent: boolean
  recentError: Error | null
  refetchRecent: () => void

  // Favorite analyses
  favoriteAnalyses: SEOAnalysis[]
  isLoadingFavorites: boolean
  favoritesError: Error | null
  refetchFavorites: () => void

  // Actions
  saveAnalysis: (data: CreateSEOAnalysisData) => Promise<SEOAnalysis>
  updateAnalysis: (data: UpdateSEOAnalysisData) => Promise<SEOAnalysis>
  deleteAnalysis: (id: string) => Promise<void>
  toggleFavorite: (id: string) => Promise<SEOAnalysis>
  recordView: (id: string) => Promise<void>

  // Loading states
  isSaving: boolean
  isUpdating: boolean
  isDeleting: boolean
}

export const useSEOAnalysisHistory = (): UseSEOAnalysisHistoryReturn => {
  const { user, isAuthenticated } = useAuth()
  const queryClient = useQueryClient()

  // Query keys
  const recentQueryKey = ['seo-analyses', 'recent', user?.id]
  const favoritesQueryKey = ['seo-analyses', 'favorites', user?.id]

  // Recent analyses query
  const {
    data: recentAnalyses = [],
    isLoading: isLoadingRecent,
    error: recentError,
    refetch: refetchRecent
  } = useQuery({
    queryKey: recentQueryKey,
    queryFn: () => seoAnalysisService.getRecentAnalyses(),
    enabled: isAuthenticated && !!user?.id,
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
  })

  // Favorite analyses query
  const {
    data: favoriteAnalyses = [],
    isLoading: isLoadingFavorites,
    error: favoritesError,
    refetch: refetchFavorites
  } = useQuery({
    queryKey: favoritesQueryKey,
    queryFn: () => seoAnalysisService.getFavoriteAnalyses(),
    enabled: isAuthenticated && !!user?.id,
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
  })

  // Save analysis mutation
  const saveAnalysisMutation = useMutation({
    mutationFn: (data: CreateSEOAnalysisData) => seoAnalysisService.saveAnalysis(data),
    onSuccess: (newAnalysis) => {
      // Update recent analyses cache
      queryClient.setQueryData(recentQueryKey, (old: SEOAnalysis[] = []) => {
        const filtered = old.filter(analysis => analysis.id !== newAnalysis.id)
        return [newAnalysis, ...filtered].slice(0, 10)
      })

      // If it's a favorite, update favorites cache too
      if (newAnalysis.is_favorite) {
        queryClient.setQueryData(favoritesQueryKey, (old: SEOAnalysis[] = []) => {
          const filtered = old.filter(analysis => analysis.id !== newAnalysis.id)
          return [newAnalysis, ...filtered]
        })
      }

      console.log('✅ SEO analysis saved and cache updated')
    },
    onError: (error) => {
      console.error('❌ Error saving SEO analysis:', error)
    }
  })

  // Update analysis mutation
  const updateAnalysisMutation = useMutation({
    mutationFn: (data: UpdateSEOAnalysisData) => seoAnalysisService.updateAnalysis(data),
    onSuccess: (updatedAnalysis) => {
      // Update both caches
      const updateCache = (old: SEOAnalysis[] = []) => {
        return old.map(analysis => 
          analysis.id === updatedAnalysis.id ? updatedAnalysis : analysis
        )
      }

      queryClient.setQueryData(recentQueryKey, updateCache)
      queryClient.setQueryData(favoritesQueryKey, updateCache)

      console.log('✅ SEO analysis updated and cache refreshed')
    },
    onError: (error) => {
      console.error('❌ Error updating SEO analysis:', error)
    }
  })

  // Delete analysis mutation
  const deleteAnalysisMutation = useMutation({
    mutationFn: (id: string) => seoAnalysisService.deleteAnalysis(id),
    onSuccess: (_, deletedId) => {
      // Remove from both caches
      const removeFromCache = (old: SEOAnalysis[] = []) => {
        return old.filter(analysis => analysis.id !== deletedId)
      }

      queryClient.setQueryData(recentQueryKey, removeFromCache)
      queryClient.setQueryData(favoritesQueryKey, removeFromCache)

      console.log('✅ SEO analysis deleted and cache updated')
    },
    onError: (error) => {
      console.error('❌ Error deleting SEO analysis:', error)
    }
  })

  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: (id: string) => seoAnalysisService.toggleFavorite(id),
    onSuccess: (updatedAnalysis) => {
      // Update recent cache
      queryClient.setQueryData(recentQueryKey, (old: SEOAnalysis[] = []) => {
        return old.map(analysis => 
          analysis.id === updatedAnalysis.id ? updatedAnalysis : analysis
        )
      })

      // Update favorites cache
      queryClient.setQueryData(favoritesQueryKey, (old: SEOAnalysis[] = []) => {
        if (updatedAnalysis.is_favorite) {
          // Add to favorites if not already there
          const exists = old.some(analysis => analysis.id === updatedAnalysis.id)
          return exists 
            ? old.map(analysis => analysis.id === updatedAnalysis.id ? updatedAnalysis : analysis)
            : [updatedAnalysis, ...old]
        } else {
          // Remove from favorites
          return old.filter(analysis => analysis.id !== updatedAnalysis.id)
        }
      })

      console.log('✅ SEO analysis favorite status toggled')
    },
    onError: (error) => {
      console.error('❌ Error toggling favorite:', error)
    }
  })

  // Record view function
  const recordView = useCallback(async (id: string) => {
    try {
      await seoAnalysisService.recordView(id)
      
      // Update view count in cache
      const updateViewCount = (old: SEOAnalysis[] = []) => {
        return old.map(analysis => 
          analysis.id === id 
            ? { ...analysis, view_count: (analysis.view_count || 0) + 1, last_viewed_at: new Date().toISOString() }
            : analysis
        )
      }

      queryClient.setQueryData(recentQueryKey, updateViewCount)
      queryClient.setQueryData(favoritesQueryKey, updateViewCount)
    } catch (error) {
      console.error('❌ Error recording view:', error)
      // Don't throw - view recording shouldn't break the app
    }
  }, [queryClient, recentQueryKey, favoritesQueryKey])

  return {
    // Data
    recentAnalyses,
    isLoadingRecent,
    recentError: recentError as Error | null,
    refetchRecent,

    favoriteAnalyses,
    isLoadingFavorites,
    favoritesError: favoritesError as Error | null,
    refetchFavorites,

    // Actions
    saveAnalysis: saveAnalysisMutation.mutateAsync,
    updateAnalysis: updateAnalysisMutation.mutateAsync,
    deleteAnalysis: deleteAnalysisMutation.mutateAsync,
    toggleFavorite: toggleFavoriteMutation.mutateAsync,
    recordView,

    // Loading states
    isSaving: saveAnalysisMutation.isPending,
    isUpdating: updateAnalysisMutation.isPending,
    isDeleting: deleteAnalysisMutation.isPending
  }
}
