import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig(({ mode }) => {
  // Cargar variables de entorno
  const env = loadEnv(mode, process.cwd(), '');

  // PUERTOS FORZADOS - NO NEGOCIABLES
  const FRONTEND_PORT = 3002;
  const BACKEND_PORT = 5001;

  return {
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@shared": path.resolve(__dirname, "../shared"),
    },
  },
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "lucide-react",
      "classnames",
      "prop-types",
      "mobx-react-lite",
      "mobx"
    ]
  },
  build: {
    outDir: "dist",
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          // Polotno (largest chunk - 4.2MB)
          'polotno': ['polotno'],

          // React ecosystem
          'react-vendor': ['react', 'react-dom'],

          // UI libraries
          'ui-vendor': ['framer-motion', 'lucide-react'],

          // Query and routing
          'query-vendor': ['@tanstack/react-query', 'wouter']
        }
      }
    }
  },
  server: {
    host: true,
    port: FRONTEND_PORT, // Frontend port (Emma Studio custom) - FORZADO SIEMPRE
    strictPort: true, // Force port 3002 to ensure consistency - NO NEGOCIABLE
    proxy: {
      '/api': {
        target: `http://127.0.0.1:${BACKEND_PORT}`, // Backend server correcto (puerto 8001) - FORZADO
        changeOrigin: true,
        secure: false, // Allow insecure connections for development
        // Enhanced logging and debugging for proxy
        rewrite: (path) => {
          // Log the path for debugging
          console.log(`Proxying request: ${path}`);
          return path;
        },
        configure: (proxy, options) => {
          // Add error handling for proxy
          proxy.on('error', (err, req, res) => {
            console.error('Proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Proxy request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Proxy response:', proxyRes.statusCode, req.url);
          });
        }
      },
    },
  },
  };
});
